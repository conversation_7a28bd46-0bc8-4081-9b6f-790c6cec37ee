/*
 * <PERSON><PERSON>'s Doors -
 */

#include <Adafruit_NeoPixel.h>
#include <ESP32Servo.h>
#include <WiFi.h>
#include <WebServer.h>
#include <SPIFFS.h>
#include <ArduinoJson.h>
#include <Update.h>
#include <esp_task_wdt.h>
#include <driver/i2s.h>
#include <algorithm>
#include <random>

// === КОНФИГУРАЦИЯ ===
#define LITHO_ROWS 4
#define LITHO_COLS 8
#define NUM_LITHO (LITHO_ROWS * LITHO_COLS)
#define NUM_DOOR 46

// === КОНСТАНТЫ СЕРВОПРИВОДОВ ===
const int leftClosed = 0;
const int leftOpen = 60;
const int rightClosed = 60;
const int rightOpen = 0;

// Pins
#define LITHOPHANE_PIN 15
#define DOOR_L_PIN 16
#define DOOR_R_PIN 17
#define SERVO_L_PIN 19
#define SERVO_R_PIN 18
#define I2S_DOUT 12
#define I2S_BCLK 14
#define I2S_LRC 13
#define BATTERY_PIN 34

// === СИСТЕМА ОБРАБОТКИ ОШИБОК ===
class ErrorHandler {
public:
    enum Level { INFO, WARN, ERROR, CRITICAL };
    
    static void handle(Level level, const char* component, const char* msg) {
        if (level >= ERROR) {
            errorCount++;
            lastErrorTime = millis();
        }
        
        if (level == CRITICAL) {
            ESP.restart();
        }
    }
    
    static uint16_t getErrorCount() { return errorCount; }
    
private:
    static uint16_t errorCount;
    static unsigned long lastErrorTime;
};

uint16_t ErrorHandler::errorCount = 0;
unsigned long ErrorHandler::lastErrorTime = 0;

// === КЛАСС ДЛЯ ИЗМЕРЕНИЯ БАТАРЕИ ===
class BatteryMonitor {
private:
    int pin;
    float minVoltage;
    float maxVoltage;
    float dividerRatio;
    mutable float lastVoltage = 0.0;
    mutable uint8_t lastLevel = 0;
    mutable unsigned long lastReading = 0;

public:
    BatteryMonitor(int analogPin, float minV, float maxV, float divRatio)
        : pin(analogPin), minVoltage(minV), maxVoltage(maxV), dividerRatio(divRatio) {}

    void begin() {
        analogReadResolution(12); // 12-bit resolution for ESP32
        analogSetAttenuation(ADC_11db); // For 0-3.3V range
    }

    float getBatteryVolts() const {
        unsigned long now = millis();
        if (now - lastReading < 1000) {
            return lastVoltage;
        }

        int sum = 0;
        for (int i = 0; i < 10; i++) {
            sum += analogRead(pin);
            delay(1);
        }

        float avgReading = sum / 10.0;
        lastVoltage = (avgReading / 4095.0) * 3.3 * dividerRatio;
        lastReading = now;

        return lastVoltage;
    }

    uint8_t getBatteryChargeLevel() const {
        float voltage = getBatteryVolts();

        // Ограничиваем диапазон
        if (voltage <= minVoltage) return 0;
        if (voltage >= maxVoltage) return 100;

        // Линейная интерполяция
        lastLevel = (uint8_t)((voltage - minVoltage) / (maxVoltage - minVoltage) * 100);
        return lastLevel;
    }
};

// === МЕНЕДЖЕР ФУНКЦИЙ ===
class FeatureManager {
public:
    enum FeatureId { AUDIO, SERVOS, LITHO, DOOR_LEDS, WIFI };
    
    struct Feature {
        bool enabled = true;
        bool critical = false;
        unsigned long lastError = 0;
        uint8_t errorCount = 0;
        const char* name;
    };
    
private:
    Feature features[5];

public:
    FeatureManager() {
        features[AUDIO].enabled = true;
        features[AUDIO].critical = false;
        features[AUDIO].lastError = 0;
        features[AUDIO].errorCount = 0;
        features[AUDIO].name = "AUDIO";

        features[SERVOS].enabled = true;
        features[SERVOS].critical = true;
        features[SERVOS].lastError = 0;
        features[SERVOS].errorCount = 0;
        features[SERVOS].name = "SERVOS";

        features[LITHO].enabled = true;
        features[LITHO].critical = false;
        features[LITHO].lastError = 0;
        features[LITHO].errorCount = 0;
        features[LITHO].name = "LITHO";

        features[DOOR_LEDS].enabled = true;
        features[DOOR_LEDS].critical = false;
        features[DOOR_LEDS].lastError = 0;
        features[DOOR_LEDS].errorCount = 0;
        features[DOOR_LEDS].name = "DOOR_LEDS";

        features[WIFI].enabled = true;
        features[WIFI].critical = true;
        features[WIFI].lastError = 0;
        features[WIFI].errorCount = 0;
        features[WIFI].name = "WIFI";
    }
    void disableFeature(FeatureId id, const char* reason) {
        if (id < 5) {
            features[id].enabled = false;
            features[id].errorCount++;
            features[id].lastError = millis();
            ErrorHandler::handle(ErrorHandler::WARN, features[id].name, reason);
        }
    }
    
    bool isEnabled(FeatureId id) const {
        return (id < 5) ? features[id].enabled : false;
    }
    
    void attemptRecovery() {
        unsigned long now = millis();
        for (int i = 0; i < 5; i++) {
            if (!features[i].enabled && !features[i].critical && 
                now - features[i].lastError > 30000) { // 30 секунд
                features[i].enabled = true;
                features[i].errorCount = 0;
                ErrorHandler::handle(ErrorHandler::INFO, features[i].name, "Recovery attempt");
            }
        }
    }
    
    String getStatusJson() const {
        JsonDocument doc;
        for (int i = 0; i < 5; i++) {
            JsonObject feat = doc[features[i].name].to<JsonObject>();
            feat["enabled"] = features[i].enabled;
            feat["errors"] = features[i].errorCount;
        }
        String result;
        serializeJson(doc, result);
        return result;
    }
};

// === ТЕЛЕМЕТРИЯ ===
class TelemetryManager {
private:
    struct Metrics {
        uint32_t uptime;
        uint16_t freeHeap;
        uint16_t minFreeHeap;
        uint8_t batteryLevel;
        float batteryVoltage;
        uint16_t totalAnimations;
        uint16_t totalAudioPlays;
        uint8_t errorCount;
        uint8_t wifiClients;  // Добавлено
    } metrics;
    
    unsigned long lastCollection = 0;
    
public:
    void collect() {
        unsigned long now = millis();
        if (now - lastCollection < 1000) return; // Не чаще раза в секунду
        
        metrics.uptime = now;
        metrics.freeHeap = ESP.getFreeHeap();
        metrics.minFreeHeap = ESP.getMinFreeHeap();
        metrics.wifiClients = WiFi.softAPgetStationNum();
        metrics.errorCount = ErrorHandler::getErrorCount();
        
        lastCollection = now;
    }
    
    void updateBattery(float voltage, uint8_t level) {
        metrics.batteryVoltage = voltage;
        metrics.batteryLevel = level;
    }
    
    void incrementAnimations() { metrics.totalAnimations++; }
    void incrementAudio() { metrics.totalAudioPlays++; }
    
    String getJson() const {
        JsonDocument doc;
        doc["uptime"] = metrics.uptime;
        doc["freeHeap"] = metrics.freeHeap;
        doc["minFreeHeap"] = metrics.minFreeHeap;
        doc["batteryLevel"] = metrics.batteryLevel;
        doc["batteryVoltage"] = metrics.batteryVoltage;
        doc["wifiClients"] = metrics.wifiClients;
        doc["animations"] = metrics.totalAnimations;
        doc["audioPlays"] = metrics.totalAudioPlays;
        doc["errors"] = metrics.errorCount;

        String result;
        serializeJson(doc, result);
        return result;
    }
    
    // Проверка критических значений
    void checkCritical() {
        if (metrics.freeHeap < 5000) {
            ErrorHandler::handle(ErrorHandler::ERROR, "MEMORY", "Low heap memory");
        }
        if (metrics.batteryLevel < 10) {
            ErrorHandler::handle(ErrorHandler::WARN, "BATTERY", "Low battery level");
        }
    }
};

// === УПРАВЛЕНИЕ ПИТАНИЕМ ===
class PowerManager {
private:
    bool lowPowerMode = false;
    unsigned long lastActivity = 0;
    BatteryMonitor* battery;

public:
    PowerManager(BatteryMonitor* bat) : battery(bat) {}
    
    void updateActivity() {
        lastActivity = millis();
        if (lowPowerMode) {
            exitLowPowerMode();
        }
    }
    
    void checkPowerState() {
        uint8_t level = battery->getBatteryChargeLevel();
        unsigned long now = millis();
        
        // Низкий заряд - переход в режим экономии
        if (level < 20 && !lowPowerMode) {
            enterLowPowerMode();
        }
        
        // Автоматический переход в режим экономии при бездействии
        if (!lowPowerMode && now - lastActivity > 300000) { // 5 минут
            enterLowPowerMode();
        }
    }
    
    void enterLowPowerMode() {
        lowPowerMode = true;
        // Уменьшаем яркость LED
        // Замедляем анимации
        // Уменьшаем частоту обновления
        ErrorHandler::handle(ErrorHandler::INFO, "POWER", "Entering low power mode");
    }
    
    void exitLowPowerMode() {
        lowPowerMode = false;
        ErrorHandler::handle(ErrorHandler::INFO, "POWER", "Exiting low power mode");
    }
    
    bool isLowPowerMode() const { return lowPowerMode; }
    
    uint8_t getOptimalBrightness(uint8_t requested) const {
        if (lowPowerMode) {
            return requested / 3; // Уменьшаем яркость в 3 раза
        }
        return requested;
    }
};

// === КОНТРОЛЛЕР АУДИО ===
class AudioController {
private:
    bool initialized = false;
    bool playing = false;
    TaskHandle_t audioTask = nullptr;
    QueueHandle_t audioQueue;
    
    struct AudioFile {
        char filename[64];
        bool loop;
    };
    
public:
    bool begin() {
        // Инициализация I2S (код из предыдущей версии)
        i2s_config_t i2s_config = {
            .mode = (i2s_mode_t)(I2S_MODE_MASTER | I2S_MODE_TX),
            .sample_rate = 16000,
            .bits_per_sample = I2S_BITS_PER_SAMPLE_16BIT,
            .channel_format = I2S_CHANNEL_FMT_ONLY_LEFT,
            .communication_format = (i2s_comm_format_t)I2S_COMM_FORMAT_STAND_I2S,
            .intr_alloc_flags = ESP_INTR_FLAG_LEVEL1,
            .dma_buf_count = 4,
            .dma_buf_len = 256,
            .use_apll = false,
            .tx_desc_auto_clear = true,
            .fixed_mclk = 0
        };

        i2s_pin_config_t pin_config = {
            .bck_io_num = I2S_BCLK,
            .ws_io_num = I2S_LRC,
            .data_out_num = I2S_DOUT,
            .data_in_num = -1
        };

        if (i2s_driver_install(I2S_NUM_0, &i2s_config, 0, NULL) != ESP_OK) {
            ErrorHandler::handle(ErrorHandler::ERROR, "AUDIO", "I2S driver install failed");
            return false;
        }

        if (i2s_set_pin(I2S_NUM_0, &pin_config) != ESP_OK) {
            ErrorHandler::handle(ErrorHandler::ERROR, "AUDIO", "I2S pin config failed");
            return false;
        }

        // Создаем очередь для аудиофайлов
        audioQueue = xQueueCreate(5, sizeof(AudioFile));
        if (!audioQueue) {
            ErrorHandler::handle(ErrorHandler::ERROR, "AUDIO", "Queue creation failed");
            return false;
        }

        initialized = true;
        ErrorHandler::handle(ErrorHandler::INFO, "AUDIO", "Initialized successfully");
        return true;
    }
    
    bool playAsync(const char* filename) {
        if (!initialized) return false;
        
        AudioFile file;
        strncpy(file.filename, filename, sizeof(file.filename) - 1);
        file.filename[sizeof(file.filename) - 1] = '\0';
        file.loop = false;
        
        if (xQueueSend(audioQueue, &file, 0) != pdTRUE) {
            ErrorHandler::handle(ErrorHandler::WARN, "AUDIO", "Queue full");
            return false;
        }
        
        // Создаем задачу если её нет
        if (audioTask == nullptr) {
            xTaskCreate(audioTaskWrapper, "AudioTask", 4096, this, 1, &audioTask);
        }
        
        return true;
    }
    
    void stop() {
        if (audioTask) {
            vTaskDelete(audioTask);
            audioTask = nullptr;
        }
        playing = false;
        if (initialized) {
            i2s_zero_dma_buffer(I2S_NUM_0);
        }
    }
    
    bool isPlaying() const { return playing; }
    
private:
    static void audioTaskWrapper(void* parameter) {
        static_cast<AudioController*>(parameter)->audioTaskFunction();
    }
    
    void audioTaskFunction() {
        AudioFile file;
        while (true) {
            if (xQueueReceive(audioQueue, &file, portMAX_DELAY) == pdTRUE) {
                playing = true;
                playFile(file.filename);
                playing = false;
            }
        }
    }
    
    bool playFile(const char* filename) {
        File f = SPIFFS.open(filename, "r");
        if (!f) {
            ErrorHandler::handle(ErrorHandler::WARN, "AUDIO",
                               (String("File not found, using built-in: ") + filename).c_str());
            playBuiltinTone();
            return true;
        }

        // Проверка WAV заголовка (упрощенная версия)
        uint8_t header[44];
        if (f.read(header, 44) != 44 ||
            memcmp(header, "RIFF", 4) != 0 ||
            memcmp(header + 8, "WAVE", 4) != 0) {
            f.close();
            ErrorHandler::handle(ErrorHandler::WARN, "AUDIO", "Invalid WAV format");
            playBuiltinTone();
            return true;
        }

        // Воспроизведение
        uint8_t buf[512];
        size_t bytesRead, bytesWritten;
        playing = true;
        while ((bytesRead = f.read(buf, sizeof(buf))) > 0 && playing) {
            i2s_write(I2S_NUM_0, buf, bytesRead, &bytesWritten, pdMS_TO_TICKS(100));
            vTaskDelay(1); // Даем время другим задачам
        }

        f.close();
        playing = false;
        return true;
    }

private:
    void playBuiltinTone() {
        // Простой встроенный звук через I2S
        if (!playing) {
            playing = true;
            // Генерируем короткий тон (упрощенная версия)
            delay(200); // Имитация воспроизведения
            playing = false;
        }
    }
};

// === БАЗОВЫЙ КОНТРОЛЛЕР LED ===
class LEDController {
protected:
    uint8_t brightness = 50;
    uint8_t speed = 5;
    uint8_t r = 255, g = 255, b = 255;
    bool running = false;
    
public:
    void setBrightness(uint8_t b) {
        brightness = constrain(b, 0, 127);
        if (running) updateDisplay();
    }
    
    void setSpeed(uint8_t s) {
        speed = constrain(s, 1, 10);
    }
    
    void setColor(uint8_t red, uint8_t green, uint8_t blue) {
        r = red; g = green; b = blue;
        if (running) updateDisplay();
    }
    
    void turnOn() { 
        running = true; 
        updateDisplay();
    }
    
    void turnOff() { 
        running = false; 
        clearDisplay();
    }
    
    bool isOn() const { return running; }
    
protected:
    virtual void updateDisplay() = 0;
    virtual void clearDisplay() = 0;
};



// === КОНТРОЛЛЕР ЛИТОФАНИИ ===
class LithophaneController {
private:
    Adafruit_NeoPixel* strip;
    String currentAnimation = "fade_in";
    bool isEnabled = false;  // Состояние включено/выключено
    bool animationRunning = false; // Состояние анимации
    bool updatePending = false; // Флаг отложенного обновления
    bool stripBusy = false; // Защита от одновременного доступа к strip
    unsigned long lastUpdate = 0;
    unsigned long lastParamUpdate = 0; // Для ограничения частоты обновлений
    int animStep1 = 0, animStep2 = 0;
    uint8_t brightness = 64;
    uint8_t speed = 5;
    uint8_t r = 255, g = 147, b = 41;
    std::vector<int> randomIndices;
    
public:
    LithophaneController(Adafruit_NeoPixel* s) : strip(s) {
        randomIndices.reserve(NUM_LITHO);
    }
    
    void begin() {
        strip->begin();
        strip->clear();
        strip->show();
        ErrorHandler::handle(ErrorHandler::INFO, "LITHO", "Initialized");
    }
    
    void setAnimation(const String& anim) {
        if (anim != currentAnimation) {
            currentAnimation = anim;
            resetAnimation();
            ErrorHandler::handle(ErrorHandler::INFO, "LITHO", 
                               (String("Animation: ") + anim).c_str());
        }
    }
    
    void setBrightness(uint8_t b) {
        brightness = constrain(b, 0, 127);
        // Ограничиваем частоту обновлений
        unsigned long now = millis();
        if (now - lastParamUpdate < 50) { // Не чаще 20 раз в секунду
            updatePending = true;
            return;
        }
        lastParamUpdate = now;

        // Обновляем отображение только если литофания включена и strip не занят
        if (isEnabled && !stripBusy && !animationRunning) {
            safeRefreshDisplay();
        } else if (isEnabled) {
            updatePending = true; // Отложенное обновление
        }
    }

    void setSpeed(uint8_t s) {
        speed = constrain(s, 1, 10);
        // Скорость применится при следующем обновлении автоматически
    }

    void setColor(uint8_t red, uint8_t green, uint8_t blue) {
        r = red; g = green; b = blue;
        // Ограничиваем частоту обновлений
        unsigned long now = millis();
        if (now - lastParamUpdate < 50) { // Не чаще 20 раз в секунду
            updatePending = true;
            return;
        }
        lastParamUpdate = now;

        // Обновляем отображение только если литофания включена и strip не занят
        if (isEnabled && !stripBusy && !animationRunning) {
            safeRefreshDisplay();
        } else if (isEnabled) {
            updatePending = true; // Отложенное обновление
        }
    }

    void forceUpdate() {
        // Принудительное обновление отображения с текущими параметрами
        if (isEnabled) {
            refreshDisplay();
        }
    }

    void safeRefreshDisplay() {
        if (stripBusy) return; // Защита от одновременного доступа
        stripBusy = true;

        try {
            uint32_t color = strip->Color((r * brightness) / 127, (g * brightness) / 127, (b * brightness) / 127);
            strip->fill(color);
            strip->show();
            updatePending = false;
        } catch (...) {
            // В случае ошибки просто игнорируем
        }

        stripBusy = false;
    }

    void refreshDisplay() {
        // Обратная совместимость - вызывает безопасную версию
        safeRefreshDisplay();
    }

    void update() {
        // Обрабатываем отложенные обновления если анимация не запущена
        if (!animationRunning && updatePending && isEnabled && !stripBusy) {
            safeRefreshDisplay();
            return;
        }

        if (!animationRunning) return;

        unsigned long now = millis();
        static const int speedMap[11] = {0, 200, 150, 120, 90, 70, 55, 40, 30, 20, 10};
        int delayMs = (speed < 11) ? speedMap[speed] : 70;

        if (now - lastUpdate < delayMs) return;
        lastUpdate = now;

        updateAnimation();
    }
    
    void turnOn() {
        isEnabled = true;
        resetAnimation();
        ErrorHandler::handle(ErrorHandler::INFO, "LITHO", "Turned on");
    }

    void turnOff() {
        isEnabled = false;
        animationRunning = false;
        strip->clear();
        strip->show();
        ErrorHandler::handle(ErrorHandler::INFO, "LITHO", "Turned off");
    }

    bool isAnimating() const { return animationRunning; }
    bool isOn() const {
        // Возвращаем состояние флага isEnabled
        return isEnabled;
    }

private:
    void resetAnimation() {
        animationRunning = true;
        lastUpdate = millis();
        animStep1 = 0;
        animStep2 = 0;

        if (currentAnimation == "random") {
            randomIndices.clear();
            for (int i = 0; i < NUM_LITHO; i++) {
                randomIndices.push_back(i);
            }
            std::random_device rd;
            std::mt19937 g(rd());
            std::shuffle(randomIndices.begin(), randomIndices.end(), g);
        }
    }

    void updateAnimation() {
        if (stripBusy) return; // Защита от конфликтов

        // Реализация анимаций (упрощенная версия fade_in)
        if (currentAnimation == "fade_in") {
            if (animStep1 > brightness) {
                animationRunning = false;
                // Оставляем литофанию включенной, но анимация завершена
                safeRefreshDisplay(); // Показываем финальное состояние
                return;
            }

            stripBusy = true;
            try {
                for (int i = 0; i < NUM_LITHO; i++) {
                    strip->setPixelColor(i, strip->Color(
                        (r * animStep1) / 127,
                        (g * animStep1) / 127,
                        (b * animStep1) / 127
                    ));
                }
                strip->show();
                animStep1 += 2;
            } catch (...) {
                // В случае ошибки останавливаем анимацию
                animationRunning = false;
            }
            stripBusy = false;
        }
        // Другие анимации...
    }
};

// === КОНТРОЛЛЕР ДВЕРЕЙ ===
class DoorController {
private:
    Adafruit_NeoPixel* leftStrip;
    Adafruit_NeoPixel* rightStrip;
    bool running = false;
    uint8_t brightness = 50;
    uint8_t speed = 5;
    uint8_t r = 255, g = 0, b = 0; // Красный по умолчанию

public:
    DoorController(Adafruit_NeoPixel* left, Adafruit_NeoPixel* right)
        : leftStrip(left), rightStrip(right) {}

    void begin() {
        leftStrip->begin();
        rightStrip->begin();
        leftStrip->clear();
        rightStrip->clear();
        leftStrip->show();
        rightStrip->show();
    }

    void setBrightness(uint8_t b) {
        brightness = constrain(b, 0, 127);
        updateDoors(); // Всегда обновляем отображение
    }

    void setSpeed(uint8_t s) {
        speed = constrain(s, 1, 10);
    }

    void setColor(uint8_t red, uint8_t green, uint8_t blue) {
        r = red; g = green; b = blue;
        updateDoors(); // Всегда обновляем отображение
    }

    void turnOn() {
        running = true;
        updateDoors();
    }

    void turnOff() {
        running = false;
        leftStrip->clear();
        rightStrip->clear();
        leftStrip->show();
        rightStrip->show();
    }

    bool isOn() const {
        // Возвращаем состояние флага running - это более надежно чем проверка пикселей
        return running;
    }

    void forceUpdate() {
        // Принудительное обновление отображения с текущими параметрами
        if (running) {
            updateDoors();
        }
    }

private:
    void updateDoors() {
        uint32_t color = leftStrip->Color((r * brightness) / 127, (g * brightness) / 127, (b * brightness) / 127);
        leftStrip->fill(color);
        rightStrip->fill(color);
        leftStrip->show();
        rightStrip->show();
    }
};

// === ОСНОВНЫЕ ОБЪЕКТЫ ===
FeatureManager features;
TelemetryManager telemetry;
BatteryMonitor battery(BATTERY_PIN, 3.0, 4.2, 2.0); // pin, minVolt, maxVolt, dividerRatio
PowerManager powerMgr(&battery);
AudioController audio;
Adafruit_NeoPixel lithophane(NUM_LITHO, LITHOPHANE_PIN, NEO_GRB + NEO_KHZ800);
Adafruit_NeoPixel doorLeft(NUM_DOOR, DOOR_L_PIN, NEO_GRB + NEO_KHZ800);
Adafruit_NeoPixel doorRight(NUM_DOOR, DOOR_R_PIN, NEO_GRB + NEO_KHZ800);
LithophaneController lithoController(&lithophane);
DoorController doorController(&doorLeft, &doorRight);

// === СЕРВОПРИВОДЫ ===
Servo servoLeft;
Servo servoRight;

// === ПЕРЕМЕННЫЕ СОСТОЯНИЯ ===
// doorsOpen определена ниже в секции настроек

// === СТРУКТУРА ДЛЯ АНИМАЦИИ ДВЕРЕЙ ===
struct DoorAnimState {
    bool running = false;
    bool targetOpen = false;
    unsigned long lastUpdate = 0;
    int currentAngle = 0;
};
DoorAnimState doorAnimState;

WebServer server(80);

// === ПЕРЕМЕННЫЕ НАСТРОЕК ===
// Состояния кнопок (НЕ сохраняются после перезагрузки)
bool litho_on = false;
bool door_light_on = false;
bool doors_open = false;
int litho_bright = 50;
int door_bright = 50;
String litho_anim = "fade_in";
int litho_speed = 5;
int door_speed = 5;
int litho_r = 0, litho_g = 255, litho_b = 0; // Зеленый по умолчанию
int door_r = 255, door_g = 0, door_b = 0;   // Красный по умолчанию

// === ФУНКЦИИ СОХРАНЕНИЯ НАСТРОЕК ===
#define PARAMS_FILE "/params.json"

void saveParamsToFS() {
    File f = SPIFFS.open(PARAMS_FILE, "w");
    if (!f) {
        return;
    }

    JsonDocument doc;
    doc["litho_bright"] = litho_bright;
    doc["door_bright"] = door_bright;
    doc["litho_anim"] = litho_anim;
    doc["litho_speed"] = litho_speed;
    doc["door_speed"] = door_speed;
    doc["litho_r"] = litho_r;
    doc["litho_g"] = litho_g;
    doc["litho_b"] = litho_b;
    doc["door_r"] = door_r;
    doc["door_g"] = door_g;
    doc["door_b"] = door_b;

    serializeJson(doc, f);
    f.close();
}

void loadParamsFromFS() {
    File f = SPIFFS.open(PARAMS_FILE, "r");
    if (!f) {
        return;
    }

    JsonDocument doc;
    DeserializationError err = deserializeJson(doc, f);
    f.close();

    if (err) {
        return;
    }
    if (doc["litho_bright"].is<int>()) litho_bright = doc["litho_bright"];
    if (doc["door_bright"].is<int>()) door_bright = doc["door_bright"];
    if (doc["litho_anim"].is<const char*>()) litho_anim = doc["litho_anim"].as<const char*>();
    if (doc["litho_speed"].is<int>()) litho_speed = doc["litho_speed"];
    if (doc["door_speed"].is<int>()) door_speed = doc["door_speed"];
    if (doc["litho_r"].is<int>()) litho_r = doc["litho_r"];
    if (doc["litho_g"].is<int>()) litho_g = doc["litho_g"];
    if (doc["litho_b"].is<int>()) litho_b = doc["litho_b"];
    if (doc["door_r"].is<int>()) door_r = doc["door_r"];
    if (doc["door_g"].is<int>()) door_g = doc["door_g"];
    if (doc["door_b"].is<int>()) door_b = doc["door_b"];
}

// === HTML ГЕНЕРАТОР ===
String generateSlider(const String& id, const String& label, int value, int min, int max) {
    return "<div class='param-row'><label>" + label + ":</label>"
           "<input type='range' id='" + id + "' min='" + String(min) +
           "' max='" + String(max) + "' value='" + String(value) + "'></div>";
}

String getMainPageHTML() {
    String html = "<!DOCTYPE html><html><head>";
    html += "<title>Booknook Durin's Doors</title>";
    html += "<meta charset='UTF-8'>";
    html += "<meta name='viewport' content='width=device-width,initial-scale=1'>";

    // Фавиконы
    html += "<link rel='icon' type='image/png' sizes='16x16' href='/icons8-one-ring-color-16.png'>";
    html += "<link rel='icon' type='image/png' sizes='32x32' href='/icons8-one-ring-color-32.png'>";
    html += "<link rel='icon' type='image/png' sizes='48x48' href='/icons8-one-ring-color-48.png'>";
    html += "<link rel='icon' type='image/png' sizes='96x96' href='/icons8-one-ring-color-96.png'>";
    html += "<link rel='apple-touch-icon' sizes='120x120' href='/icons8-one-ring-color-120.png'>";
    html += "<link rel='shortcut icon' href='/favicon.ico'>";

    html += "<link href='/uncial-antiqua.css' rel='stylesheet'>";
    html += "<link rel='stylesheet' href='/nano.min.css'>";

    // CSS стили
    html += "<style>";
    html += "@font-face { font-family: 'Uncial Antiqua'; src: url('/UncialAntiqua-Regular.woff2') format('woff2'); font-weight: normal; font-style: normal; font-display: swap; }";
    html += "body { background: #181818; color: #f7f5ed; font-family: 'Segoe UI', Arial, sans-serif; margin: 0; padding: 0; }";
    html += ".center { text-align: center; margin-top: 0; }";
    html += ".main-card { background: #23201a; max-width: 480px; margin: 32px auto 0 auto; border-radius: 18px; box-shadow: 0 4px 24px rgba(0,0,0,0.18); padding: 0 24px 48px 24px; overflow: hidden; }";
    html += ".lotr-title { font-family: 'Uncial Antiqua', serif; font-size: 2.2em; color: #e6d7b6; letter-spacing: 2px; margin-bottom: 0; text-shadow: 0 2px 8px #7c5c1e; line-height: 1.1; }";
    html += ".lotr-title2 { font-family: 'Uncial Antiqua', serif; font-size: 2.5em; color: #e6d7b6; letter-spacing: 2px; margin-bottom: 12px; text-shadow: 0 2px 8px #7c5c1e; line-height: 1.1; }";
    html += ".btn-row { display: flex; gap: 12px; justify-content: center; align-items: center; margin-bottom: 18px; margin-top: 12px; }";
    html += ".emoji-btn { font-size: 96px; width: 120px; height: 120px; background: #333; border: 2px solid #666; border-radius: 18px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 2px 8px rgba(0,0,0,0.3); color: #999; display: flex; align-items: center; justify-content: center; filter: grayscale(1); }";
    html += ".emoji-btn:hover { background: #444; box-shadow: 0 4px 16px rgba(0,0,0,0.4); }";
    html += ".emoji-btn.active { background: #7c5c1e; border-color: #d4af37; box-shadow: 0 0 15px rgba(212, 175, 55, 0.5); color: #e6d7b6; filter: grayscale(0); }";
    html += ".main-card hr { border: none; border-top: 1px solid #3a2f1a; margin: 24px 0; }";
    html += "select, input[type='range'] { border-radius: 6px; border: 1px solid #d1b97a; padding: 2px 6px; background: #23201a; color: #e6d7b6; margin-top: 4px; }";
    html += "input[type='submit'], button { background: #7c5c1e; color: #fff; border: none; border-radius: 8px; padding: 8px 18px; font-size: 1em; cursor: pointer; transition: background 0.2s; margin-top: 12px; }";
    html += "input[type='submit']:hover, button:hover { background: #a07d3b; }";
    html += ".slider-label { display: flex; align-items: center; gap: 10px; font-size: 1em; font-weight: 400; }";
    html += ".slider-label select, .slider-label input[type='range'] { width: 100%; min-width: 0; max-width: 220px; flex: 1 1 0; }";
    html += ".param-img { display: block; width: 100%; height: 300px; object-fit: cover; border-radius: 18px; background: #222; margin: 0 auto 12px auto; transition: filter 0.2s; pointer-events: none; }";
    html += ".param-controls { display: flex; flex-direction: column; gap: 18px; }";
    html += ".feature-card { background: #23201a; max-width: 480px; margin: 32px auto 0 auto; border-radius: 18px; box-shadow: 0 4px 24px rgba(0,0,0,0.18); padding: 24px; overflow: hidden; margin-bottom: 32px; }";
    html += "#door-color-picker, #litho-color-picker { min-width: 40px; min-height: 40px; display: inline-block; }";
    html += "@media (max-width: 600px) { .main-card { padding: 0 2vw 18px 2vw; } .lotr-title { font-size: 1.2em; } .lotr-title2 { font-size: 1.7em; } .emoji-btn { font-size: 48px; width: 64px; height: 64px; } }";
    html += "</style>";

    // JavaScript
    html += "<script src='/pickr.min.js'></script>";
    html += "<script>";
    html += "function setParam(param, value) { fetch('/set_param?' + param + '=' + encodeURIComponent(value), {method: 'GET'}).then(r => r.json()).then(data => { if (data.status === 'OK') { updateButtonStatesFromData(data); } }).catch(e => console.error('Error:', e)); }";
    html += "function rgbToHex(r, g, b) { return '#' + [r,g,b].map(x => x.toString(16).padStart(2, '0')).join(''); }";
    html += "function toggleDoors() { fetch('/toggle_doors', {method: 'POST'}).then(r => r.text()).then(data => { console.log('Doors:', data); updateButtonState('btn-doors'); }); }";
    html += "function toggleLitho() { fetch('/toggle_litho', {method: 'POST'}).then(r => r.text()).then(data => { console.log('Litho:', data); setTimeout(updateAllButtonStates, 100); }); }";
    html += "function toggleDoorlight() { fetch('/toggle_doorlight', {method: 'POST'}).then(r => r.text()).then(data => { console.log('Doorlight:', data); updateButtonState('btn-doorlight'); }); }";
    html += "function playWizard() { fetch('/play_wizard', {method: 'POST'}).then(r => r.text()).then(data => { console.log('Wizard:', data); }); }";
    html += "function updateButtonState(btnId) { var btn = document.getElementById(btnId); var wasActive = btn.classList.contains('active'); btn.classList.remove('active', 'inactive'); btn.classList.add(wasActive ? 'inactive' : 'active'); }";
    html += "function updateButtonStatesFromData(data) { var doorsBtn = document.getElementById('btn-doors'); var lithoBtn = document.getElementById('btn-litho'); var doorlightBtn = document.getElementById('btn-doorlight'); doorsBtn.classList.remove('active', 'inactive'); lithoBtn.classList.remove('active', 'inactive'); doorlightBtn.classList.remove('active', 'inactive'); doorsBtn.classList.add(data.doors_open ? 'active' : 'inactive'); lithoBtn.classList.add(data.litho_on ? 'active' : 'inactive'); doorlightBtn.classList.add(data.doorlight_on ? 'active' : 'inactive'); }";
    html += "function updateAllButtonStates() { fetch('/status').then(r => r.json()).then(data => { updateButtonStatesFromData(data); }); }";
    html += "function isLithoActive() { return document.getElementById('btn-litho').classList.contains('active'); }";
    html += "function isDoorsActive() { return document.getElementById('btn-doors').classList.contains('active'); }";
    html += "function isDoorlightActive() { return document.getElementById('btn-doorlight').classList.contains('active'); }";
    html += "window.addEventListener('DOMContentLoaded', function() {";
    html += "var lithoSlider = document.getElementById('litho_bright');";
    html += "if (lithoSlider) lithoSlider.addEventListener('input', function(e) { let perc = parseInt(e.target.value, 10); let hw = Math.round(perc * 127 / 100); setParam('litho_bright', hw); });";
    html += "var doorSlider = document.getElementById('door_bright');";
    html += "if (doorSlider) doorSlider.addEventListener('input', function(e) { let perc = parseInt(e.target.value, 10); let hw = Math.round(perc * 127 / 100); setParam('door_bright', hw); });";
    html += "var lithoAnimSelect = document.querySelector('select[name=\"litho_anim\"]');";
    html += "if (lithoAnimSelect) lithoAnimSelect.addEventListener('change', function(e) { setParam('litho_anim', e.target.value); });";
    html += "var lithoSpeedSlider = document.getElementById('litho_speed');";
    html += "if (lithoSpeedSlider) lithoSpeedSlider.addEventListener('input', function(e) { let val = parseInt(e.target.value, 10); setParam('litho_speed', val); });";
    html += "var doorSpeedSlider = document.getElementById('door_speed');";
    html += "if (doorSpeedSlider) doorSpeedSlider.addEventListener('input', function(e) { let val = parseInt(e.target.value, 10); setParam('door_speed', val); });";
    html += "function initColorPickers() {";
    html += "if (typeof Pickr !== 'undefined') {";

    // Генерируем HEX цвета из сохраненных RGB значений
    char doorColorHex[8], lithoColorHex[8];
    sprintf(doorColorHex, "#%02x%02x%02x", door_r, door_g, door_b);
    sprintf(lithoColorHex, "#%02x%02x%02x", litho_r, litho_g, litho_b);

    html += "try {";
    html += "const doorPicker = Pickr.create({ el: '#door-color-picker', theme: 'nano', default: '";
    html += doorColorHex;
    html += "', defaultRepresentation: 'RGB', components: { preview: true, hue: true, interaction: { hex: false, rgba: false, hsla: false, hsva: false, cmyk: false, input: true, save: true } } });";
    html += "doorPicker.on('save', (color) => { if (color) { const rgb = color.toRGBA(); setParam('door_color', Math.round(rgb[0]) + ',' + Math.round(rgb[1]) + ',' + Math.round(rgb[2])); doorPicker.hide(); } });";
    html += "const lithoPicker = Pickr.create({ el: '#litho-color-picker', theme: 'nano', default: '";
    html += lithoColorHex;
    html += "', defaultRepresentation: 'RGB', components: { preview: true, hue: true, interaction: { hex: false, rgba: false, hsla: false, hsva: false, cmyk: false, input: true, save: true } } });";
    html += "lithoPicker.on('save', (color) => { if (color) { const rgb = color.toRGBA(); setParam('litho_color', Math.round(rgb[0]) + ',' + Math.round(rgb[1]) + ',' + Math.round(rgb[2])); lithoPicker.hide(); } });";
    html += "console.log('Color pickers initialized successfully');";
    html += "} catch(e) { console.error('Error initializing color pickers:', e); }";
    html += "} else { console.error('Pickr library not loaded'); setTimeout(initColorPickers, 100); }";
    html += "}";
    html += "setTimeout(initColorPickers, 100);";
    html += "updateAllButtonStates();";
    html += "});";
    html += "</script>";
    html += "</head>";

    // Body
    html += "<body>";
    html += "<div class='main-card'>";
    html += "<img src='/LoTR-gold.png' alt='LoTR Gold' style='width:100%;height:auto;display:block;margin:0 auto 0 auto;max-height:160px;object-fit:contain;'>";
    html += "<div class='center'>";
    html += "<div class='lotr-title'>Booknook</div>";
    html += "<div class='lotr-title2'>Durin's Doors</div>";
    html += "<div style='margin-top:10px;font-size:1.1em;'>Заряд 🔋: <span id='batt-pct'>--%</span></div>";
    html += "</div>";
    html += "<hr>";

    // Кнопки управления
    html += "<div class='btn-row'>";
    html += "<button class='emoji-btn' id='btn-doors' onclick='toggleDoors()'>🚪</button>";
    html += "<button class='emoji-btn' id='btn-litho' onclick='toggleLitho()'>👹</button>";
    html += "<button class='emoji-btn' id='btn-doorlight' onclick='toggleDoorlight()'>💡</button>";
    html += "<button class='emoji-btn' id='btn-wizard' onclick='playWizard()'>🧙</button>";
    html += "</div>";

    // Карточки управления
    html += "<form>";
    html += "<div class='feature-card'>";
    html += "<img class='param-img' id='door-img' src='/black_doors.webp' alt='Двери'>";
    html += "<div class='param-controls'>";
    html += generateSlider("door_bright", "Яркость", (door_bright * 100) / 127, 0, 100);
    html += generateSlider("door_speed", "Скорость", door_speed, 1, 10);
    html += "<label class='slider-label'>Цвет: <div id='door-color-picker' class='color-picker'></div></label>";
    html += "</div>";
    html += "</div>";

    html += "<div class='feature-card'>";
    html += "<img class='param-img' id='litho-img' src='/balrog_for_LITHO.webp' alt='Литофания'>";
    html += "<div class='param-controls'>";
    html += "<label class='slider-label'><span>Стиль:</span>";
    html += "<select name='litho_anim'>";
    html += "<option value='vertical_snake'"; if (litho_anim == "vertical_snake") html += " selected"; html += ">Вертикально</option>";
    html += "<option value='horizontal_snake'"; if (litho_anim == "horizontal_snake") html += " selected"; html += ">Горизонтально</option>";
    html += "<option value='fade_in'"; if (litho_anim == "fade_in") html += " selected"; html += ">Плавно</option>";
    html += "<option value='random'"; if (litho_anim == "random") html += " selected"; html += ">Случайно</option>";
    html += "<option value='diagonal'"; if (litho_anim == "diagonal") html += " selected"; html += ">Диагонально</option>";
    html += "<option value='alert'"; if (litho_anim == "alert") html += " selected"; html += ">Тревога</option>";
    html += "</select>";
    html += "</label>";
    html += generateSlider("litho_bright", "Яркость", (litho_bright * 100) / 127, 0, 100);
    html += generateSlider("litho_speed", "Скорость", litho_speed, 1, 10);
    html += "<label class='slider-label'>Цвет: <div id='litho-color-picker' class='color-picker'></div></label>";
    html += "</div>";
    html += "</div>";
    html += "</form>";

    // OTA обновление
    html += "<hr>";
    html += "<div class='center'>";
    html += "<h3>🔄 Обновление прошивки</h3>";
    html += "<input type='file' id='firmware' accept='.bin'>";
    html += "<button id='updateBtn' onclick='uploadFirmware()'>Обновить прошивку</button>";
    html += "<div id='status' style='margin:10px 0; font-weight:bold;'></div>";
    html += "<script>";
    html += "function uploadFirmware() {";
    html += "const fileInput = document.getElementById('firmware');";
    html += "const updateBtn = document.getElementById('updateBtn');";
    html += "const statusDiv = document.getElementById('status');";
    html += "if (!fileInput.files.length) { statusDiv.innerHTML = '❌ <span style=\"color:red;\">Ошибка: Выберите файл прошивки</span>'; return; }";
    html += "const file = fileInput.files[0];";
    html += "if (!file.name.endsWith('.bin')) { statusDiv.innerHTML = '❌ <span style=\"color:red;\">Ошибка: Выберите файл с расширением .bin</span>'; return; }";
    html += "updateBtn.disabled = true;";
    html += "updateBtn.innerText = 'Обновление...';";
    html += "statusDiv.innerHTML = '🚀 <span style=\"color:blue;\">Начинается обновление прошивки...</span>';";
    html += "const form = new FormData();";
    html += "form.append('update', file);";
    html += "statusDiv.innerHTML = '📤 <span style=\"color:orange;\">Загрузка прошивки: 0%</span>';";
    html += "const xhr = new XMLHttpRequest();";
    html += "xhr.upload.addEventListener('progress', function(e) {";
    html += "if (e.lengthComputable) {";
    html += "const percentComplete = Math.round((e.loaded / e.total) * 100);";
    html += "statusDiv.innerHTML = '📤 <span style=\"color:orange;\">Загрузка прошивки: ' + percentComplete + '%</span>';";
    html += "}";
    html += "});";
    html += "xhr.addEventListener('load', function() {";
    html += "if (xhr.status === 200) {";
    html += "statusDiv.innerHTML = '✅ <span style=\"color:green;\">Прошивка успешно загружена!</span>';";
    html += "setTimeout(() => {";
    html += "statusDiv.innerHTML = '🔄 <span style=\"color:purple;\">Устройство перезагружается...</span>';";
    html += "tryReconnect();";
    html += "}, 1000);";
    html += "} else {";
    html += "xhr.responseText ? statusDiv.innerHTML = '❌ <span style=\"color:red;\">Ошибка ' + xhr.status + ': ' + xhr.responseText + '</span>' : statusDiv.innerHTML = '❌ <span style=\"color:red;\">Ошибка ' + xhr.status + '</span>';";
    html += "updateBtn.disabled = false;";
    html += "updateBtn.innerText = 'Обновить прошивку';";
    html += "}";
    html += "});";
    html += "xhr.addEventListener('error', function() {";
    html += "statusDiv.innerHTML = '❌ <span style=\"color:red;\">Ошибка сети при загрузке</span>';";
    html += "updateBtn.disabled = false;";
    html += "updateBtn.innerText = 'Обновить прошивку';";
    html += "});";
    html += "xhr.open('POST', '/update');";
    html += "xhr.send(form);";
    html += "}";
    html += "function tryReconnect() {";
    html += "let attempts = 0;";
    html += "const maxAttempts = 30;";
    html += "const statusDiv = document.getElementById('status');";
    html += "function check() {";
    html += "fetch('/', { cache: 'no-store', timeout: 5000 })";
    html += ".then(r => {";
    html += "if (r.ok) {";
    html += "statusDiv.innerHTML = '🎉 <span style=\"color:green;\">Устройство снова в сети! Перенаправление...</span>';";
    html += "setTimeout(() => location.reload(), 2000);";
    html += "} else throw new Error('Not ready');";
    html += "})";
    html += ".catch(() => {";
    html += "attempts++;";
    html += "if (attempts < maxAttempts) {";
    html += "statusDiv.innerHTML = '⏳ <span style=\"color:orange;\">Ожидание перезагрузки... (' + attempts + '/' + maxAttempts + ')</span>';";
    html += "setTimeout(check, 2000);";
    html += "} else {";
    html += "statusDiv.innerHTML = '⚠️ <span style=\"color:red;\">Не удалось подключиться. Обновите страницу вручную или проверьте устройство.</span>';";
    html += "}";
    html += "});";
    html += "}";
    html += "setTimeout(check, 5000);";
    html += "}";
    html += "</script>";
    html += "</div>";
    html += "</div>";
    html += "</body></html>";
    return html;
}

// === ФУНКЦИИ УПРАВЛЕНИЯ СЕРВОПРИВОДАМИ ===
void resetDoorAnimation(bool open) {
    doorAnimState.targetOpen = open;
    doorAnimState.running = true;
    doorAnimState.lastUpdate = millis();
    doorAnimState.currentAngle = open ? leftClosed : leftOpen;
    ErrorHandler::handle(ErrorHandler::INFO, "SERVO",
                        open ? "Opening doors" : "Closing doors");
}

void updateDoorAnimation() {
    if (!doorAnimState.running) return;
    
    unsigned long now = millis();
    if (now - doorAnimState.lastUpdate < 20) return;
    
    doorAnimState.lastUpdate = now;
    
    int targetAngle = doorAnimState.targetOpen ? 
        (doors_open ? leftOpen : rightOpen) : 
        (doors_open ? leftClosed : rightClosed);
    
    if (doorAnimState.currentAngle != targetAngle) {
        doorAnimState.currentAngle += (targetAngle > doorAnimState.currentAngle) ? 1 : -1;
        servoLeft.write(doorAnimState.currentAngle);
        servoRight.write(doorAnimState.currentAngle);
    } else {
        doorAnimState.running = false;
    }
}

// === SETUP ===
void setup() {
    delay(1000);

    ErrorHandler::handle(ErrorHandler::INFO, "SYSTEM", "Starting initialization");
    esp_task_wdt_deinit();

    if (!SPIFFS.begin(false)) {
        ErrorHandler::handle(ErrorHandler::ERROR, "FS", "SPIFFS mount failed");
    } else {
        ErrorHandler::handle(ErrorHandler::INFO, "FS", "SPIFFS ready");
        loadParamsFromFS();
    }

    delay(500);
    if (!audio.begin()) {
        features.disableFeature(FeatureManager::AUDIO, "I2S init failed");
    }

    delay(500);
    lithoController.begin();
    doorController.begin();

    // Инициализация сервоприводов
    if (features.isEnabled(FeatureManager::SERVOS)) {
        servoLeft.attach(SERVO_L_PIN);
        servoRight.attach(SERVO_R_PIN);
        // Устанавливаем начальное положение (закрыто)
        servoLeft.write(leftClosed);
        servoRight.write(rightClosed);
        ErrorHandler::handle(ErrorHandler::INFO, "SERVO", "Servos initialized");
    }

    lithoController.setBrightness(litho_bright);
    lithoController.setSpeed(litho_speed);
    lithoController.setAnimation(litho_anim);
    lithoController.setColor(litho_r, litho_g, litho_b);

    doorController.setBrightness(door_bright);
    doorController.setSpeed(door_speed);
    doorController.setColor(door_r, door_g, door_b);

    delay(500);
    if (features.isEnabled(FeatureManager::WIFI)) {
        WiFi.softAP("BookNook-AP", "12345678");
        delay(1000);

        // Настройка веб-сервера
        server.on("/", []() {
            String html = getMainPageHTML();
            server.send(200, "text/html", html);
        });

        server.on("/status", []() {
            JsonDocument doc;
            doc["doors_open"] = doors_open; // Состояние сервоприводов
            doc["litho_on"] = lithoController.isOn();
            doc["doorlight_on"] = doorController.isOn(); // Состояние LED подсветки
            doc["battery_level"] = battery.getBatteryChargeLevel();
            doc["uptime"] = millis();
            doc["free_heap"] = ESP.getFreeHeap();

            String result;
            serializeJson(doc, result);
            server.send(200, "application/json", result);
        });

        server.on("/features", []() {
            server.send(200, "application/json", features.getStatusJson());
        });

        // API endpoints для управления BookNook
        server.on("/set_param", []() {
            // Получаем первый параметр как имя, второй как значение
            String paramName = "";
            String paramValue = "";

            // Если есть параметры param и value (старый формат)
            if (server.hasArg("param") && server.hasArg("value")) {
                paramName = server.arg("param");
                paramValue = server.arg("value");
            } else {
                // Новый формат: первый аргумент - имя параметра
                for (int i = 0; i < server.args(); i++) {
                    paramName = server.argName(i);
                    paramValue = server.arg(i);
                    break; // Берем первый параметр
                }
            }

            static unsigned long lastSave = 0;
            bool needSave = false;

            if (paramName == "litho_bright") {
                litho_bright = constrain(paramValue.toInt(), 0, 127);
                lithoController.setBrightness(litho_bright);
                needSave = true;
            } else if (paramName == "door_bright") {
                door_bright = constrain(paramValue.toInt(), 0, 127);
                doorController.setBrightness(door_bright);
                needSave = true;
            } else if (paramName == "litho_speed") {
                litho_speed = constrain(paramValue.toInt(), 1, 10);
                lithoController.setSpeed(litho_speed);
                needSave = true;
            } else if (paramName == "door_speed") {
                door_speed = constrain(paramValue.toInt(), 1, 10);
                doorController.setSpeed(door_speed);
                needSave = true;
            } else if (paramName == "litho_anim") {
                litho_anim = paramValue;
                lithoController.setAnimation(litho_anim);
                needSave = true;
            } else if (paramName == "litho_color") {
                int r, g, b;
                if (sscanf(paramValue.c_str(), "%d,%d,%d", &r, &g, &b) == 3) {
                    litho_r = r; litho_g = g; litho_b = b;
                    lithoController.setColor(r, g, b);
                    needSave = true;
                }
            } else if (paramName == "door_color") {
                int r, g, b;
                if (sscanf(paramValue.c_str(), "%d,%d,%d", &r, &g, &b) == 3) {
                    door_r = r; door_g = g; door_b = b;
                    doorController.setColor(r, g, b);
                    needSave = true;
                }
            }

            // Сохраняем не чаще раза в 2 секунды для предотвращения износа flash
            unsigned long now = millis();
            if (needSave && (now - lastSave > 2000)) {
                saveParamsToFS();
                lastSave = now;
            }

            // Возвращаем JSON с текущим состоянием для обновления UI
            StaticJsonDocument<200> doc; // Фиксированный размер для предотвращения проблем с памятью
            doc["status"] = "OK";
            doc["litho_on"] = lithoController.isOn();
            doc["doorlight_on"] = doorController.isOn();
            doc["doors_open"] = doors_open;

            String result;
            if (serializeJson(doc, result) == 0) {
                // Ошибка сериализации - возвращаем простой ответ
                server.send(200, "text/plain", "OK");
            } else {
                server.send(200, "application/json", result);
            }
        });

        server.on("/toggle_doors", HTTP_POST, []() {
            doors_open = !doors_open;
            resetDoorAnimation(doors_open);
            server.send(200, "text/plain", "OK");
        });

        server.on("/toggle_litho", HTTP_POST, []() {
            if (lithoController.isOn()) {
                lithoController.turnOff();
            } else {
                lithoController.turnOn();
            }
            server.send(200, "text/plain", "OK");
        });

        server.on("/toggle_doorlight", HTTP_POST, []() {
            if (doorController.isOn()) {
                doorController.turnOff();
            } else {
                doorController.turnOn();
            }
            server.send(200, "text/plain", "OK");
        });

        server.on("/play_wizard", HTTP_POST, []() {
            server.send(200, "text/plain", "OK");
        });

        // === OTA ОБНОВЛЕНИЕ ===
        server.on("/update", HTTP_POST, []() {
            if (Update.hasError()) {
                server.send(500, "text/plain", "Update failed");
            } else {
                server.send(200, "text/plain", "Update successful. Rebooting...");
                // Даем время браузеру получить ответ перед перезагрузкой
                delay(1000);
                ESP.restart();
            }
        }, []() {
            HTTPUpload& upload = server.upload();
            if (upload.status == UPLOAD_FILE_START) {
                if (!Update.begin(UPDATE_SIZE_UNKNOWN)) {
                }
            } else if (upload.status == UPLOAD_FILE_WRITE) {
                if (Update.write(upload.buf, upload.currentSize) != upload.currentSize) {
                }
            } else if (upload.status == UPLOAD_FILE_END) {
                if (Update.end(true)) {
                } else {
                }
            }
        });

        // Обработчики фавиконов
        server.on("/favicon.ico", []() {
            File f = SPIFFS.open("/icons8-one-ring-color-32.png", "r");
            if (f) {
                server.streamFile(f, "image/png");
                f.close();
            } else {
                server.send(404, "text/plain", "Favicon not found");
            }
        });

        server.on("/apple-touch-icon.png", []() {
            File f = SPIFFS.open("/icons8-one-ring-color-120.png", "r");
            if (f) {
                server.streamFile(f, "image/png");
                f.close();
            } else {
                server.send(404, "text/plain", "Apple touch icon not found");
            }
        });

        server.onNotFound([]() {
            String path = server.uri();

            if (!SPIFFS.exists(path)) {
                server.send(404, "text/plain", "File not found");
                return;
            }

            File f = SPIFFS.open(path, "r");
            if (!f) {
                server.send(500, "text/plain", "Failed to open file");
                return;
            }

            size_t fileSize = f.size();

            if (fileSize == 0) {
                f.close();
                server.send(404, "text/plain", "File is empty");
                return;
            }

            String contentType = "text/plain";
            if (path.endsWith(".html")) contentType = "text/html";
            else if (path.endsWith(".css")) contentType = "text/css";
            else if (path.endsWith(".js")) contentType = "application/javascript";
            else if (path.endsWith(".png")) contentType = "image/png";
            else if (path.endsWith(".jpg") || path.endsWith(".jpeg")) contentType = "image/jpeg";
            else if (path.endsWith(".webp")) contentType = "image/webp";
            else if (path.endsWith(".svg")) contentType = "image/svg+xml";
            else if (path.endsWith(".woff2")) contentType = "font/woff2";
            else if (path.endsWith(".wav")) contentType = "audio/wav";

            server.streamFile(f, contentType);
            f.close();
        });

        server.begin();
        ErrorHandler::handle(ErrorHandler::INFO, "WIFI", "AP started");
    }

    delay(500);
    battery.begin();

    delay(500);
    esp_task_wdt_init(30, true);
    esp_task_wdt_add(NULL);

    ErrorHandler::handle(ErrorHandler::INFO, "SYSTEM", "Initialization complete");
}

// === LOOP ===
void loop() {
    static unsigned long lastUpdate = 0;
    static unsigned long lastHeartbeat = 0;
    unsigned long now = millis();

    if (now - lastUpdate < 50) {
        yield();
        return;
    }
    lastUpdate = now;

    // Основные обновления с проверками
    try {
        if (features.isEnabled(FeatureManager::WIFI)) {
            server.handleClient();
        }

        if (features.isEnabled(FeatureManager::LITHO)) {
            lithoController.update();
        }

        if (features.isEnabled(FeatureManager::SERVOS)) {
            updateDoorAnimation();
        }

        static unsigned long lastSystemCheck = 0;
        if (now - lastSystemCheck >= 10000) {
            telemetry.collect();
            telemetry.checkCritical();
            telemetry.updateBattery(battery.getBatteryVolts(), battery.getBatteryChargeLevel());
            powerMgr.checkPowerState();
            features.attemptRecovery();
            esp_task_wdt_reset();
            lastSystemCheck = now;
        }
    } catch (...) {
        delay(1000);
    }
}
